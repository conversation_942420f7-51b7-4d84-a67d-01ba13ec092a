function getProfession(req,res) {
    const cache = req.cache
    const code = req.query.code
    const professionData = cache.profession
    if(code==undefined) {
        const data = []
        for(var key in professionData) {
            data.push(professionData[key])
        }
        return res.status(200).json({
            code : 1,
            msg : "get profession list successfully",
            data : data
        })
    }
    else {
        if(professionData.hasOwnProperty(code)) {
            return res.status(200).json({
                code : 1,
                msg : "get profession successfully",
                data : professionData[code]
            })
        }
        else {
            return res.status(200).json({
                code : 0,
                msg : "invalid code"
            })
        }
    }   
}

function getJobtype(req,res) {
    const cache = req.cache
    const code = req.query.code
    const jobType = cache.jobType
    if(code==undefined) {
        const data = []
        for(var key in jobType) {
            data.push(jobType[key])
        }
        return res.status(200).json({
            code : 1,
            msg : "get job type list successfully",
            data : data
        })
    }
    else {
        if(jobType.hasOwnProperty(code)) {
            return res.status(200).json({
                code : 1,
                msg : "get job type successfully",
                data : jobType[code]
            })
        }
        else {
            return res.status(200).json({
                code : 0,
                msg : "invalid code"
            })
        }
    }
}

function getEmploymentType(req,res) {
    const cache = req.cache
    const code = req.query.code
    const employmentType = cache.employmentTypeFull
    if(code==undefined) {
        const data = []
        for(var key in employmentType) {
            data.push(employmentType[key])
        }
        return res.status(200).json({
            code : 1,
            msg : "get employment list successfully",
            data : data
        })
    }
    else {
        if(employmentType.hasOwnProperty(code)) {
            return res.status(200).json({
                code : 1,
                msg : "get employment type successfully",
                data : employmentType[code]
            })
        }
        else {
            return res.status(200).json({
                code : 0,
                msg : "invalid code"
            })
        }
    }
}

module.exports = {
    getProfession,
    getJobtype,
    getEmploymentType
}