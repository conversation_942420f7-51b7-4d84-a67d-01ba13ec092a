def registryCredential = 'harbor_user'
pipeline {
    agent any

    environment {
        APP_NAME = 'masterdata' //Replace
        HARBOR_URL = 'https://els-registry.evnfc.vn'
    }


    stages {
        stage("Checkout code") {
            steps {
              checkout scm
            }
        }

        //stage('Print env') {
        //    steps {
        //        sh 'printenv'
        //    }
        //}
  
        stage('Build image') {

            steps {
    
                script {
                    imageId = "els-registry.evnfc.vn/els/${APP_NAME}:$BUILD_NUMBER"
                    docker.withRegistry(HARBOR_URL, registryCredential) {
                        myapp = docker.build(imageId, '-f Dockerfile .')
                        myapp.push()
                    }
                }
            }
        }
        
        stage('Deploy to UAT') {
            when {
                environment name: 'GIT_BRANCH', value: 'origin/uat'
            }

            steps {
                git branch: 'main',
                    credentialsId: 'user-els-gitlab-ssh',
                    url: '***********************:devops/config.git'

                sh """#!/bin/bash
                      git config --global user.email "<EMAIL>"
                      git config --global user.name "Jenkins"
                      cd services/els/${APP_NAME}
                      sed -i "s|tag: .*|tag: ${BUILD_NUMBER}|" values-uat.yaml
                      git add .
                      git commit -m 'Deploy ${APP_NAME} version ${BUILD_NUMBER}'
                      git push origin main
                   """
            }
        }
    }
}

