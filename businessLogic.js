const camelcaseKeys = require("camelcase-keys");
const { levenshteinDistance } = require("./common");
const {
  getSmeMappingCode,
  getSmeConfigKey,
} = require("./repository/sme_mapping_repo");
const helper = require("./utils/helper");

const getBusinessUnit = function (req, res, cache, common) {
  try {
  } catch (error) {
    common.log(error);
    res.status(502).json({
      error: 1,
      msg: "Service Error",
    });
  }
};
const getProvince = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.code == undefined) {
      res.status(400).json({
        error: 1,
        msg: "Ma Tinh/TP bat buoc nhap",
      });
    } else {
      const partnerCode = query.partnerCode;
      const code = query.code;
      if (!cache.province[partnerCode].hasOwnProperty(code)) {
        return res.status(200).json({
          error: 1,
          msg: "invalid province code",
        });
      }
      return res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache.province[partnerCode][code]),
      });
    }
  } catch (error) {
    common.log(error);
    res.status(502).json({
      error: 1,
      msg: "Service Error",
    });
  }
};
const getDistrict = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.code == undefined) {
      res.status(400).json({
        error: 1,
        msg: "Ma Quan/Huyen la bat buoc",
      });
    } else {
      const partnerCode = query.partnerCode;
      const code = query.code;
      if (!cache.district[partnerCode].hasOwnProperty(code)) {
        return res.status(200).json({
          error: 1,
          msg: "invalid district code",
        });
      }
      return res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache.district[partnerCode][code]),
      });
    }
  } catch (error) {
    common.log(error);
    res.status(502).json({
      error: 1,
      msg: "Service Error",
    });
  }
};
const getWard = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.code == undefined) {
      res.status(400).json({
        error: 1,
        msg: "Ma Xa/Phuong la bat buoc",
      });
    } else {
      const partnerCode = query.partnerCode;
      const code = query.code;
      if (!cache.ward[partnerCode].hasOwnProperty(code)) {
        return res.status(200).json({
          error: 1,
          msg: "invalid ward code",
        });
      }
      return res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache.ward[partnerCode][code]),
      });
    }
  } catch (error) {
    common.log(error);
    res.status(502).json({
      error: 1,
      msg: "Service Error",
    });
  }
};

const getEmploymentType = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.employmentType == undefined) {
      res.status(400).json({
        error: 1,
        msg: "Ma Xa/Phuong la bat buoc",
      });
    } else if (query.employmentType in cache["employmentType"]) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["employmentType"][query.employmentType]),
      });
    } else {
      res.status(404).json({
        error: 1,
        msg: "invalid code",
      });
    }
  } catch (error) { }
};

const getRefType = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.refType == undefined) {
      res.status(400).json({
        error: 1,
        msg: "ref type is undefined",
      });
    } else if (query.refType in cache["refType"]) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["refType"][query.refType]),
      });
    }
  } catch (error) {
    res.status(404).json({
      error: 1,
      msg: "invalid code",
    });
  }
};
const getBundle = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.code == undefined) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["bundle"]),
      });
    } else if (query.code in cache["bundle"]) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["bundle"][query.code]),
      });
    }
  } catch (error) {
    res.status(404).json({
      error: 1,
      msg: "invalid code",
    });
  }
};
const getDocument = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.code == undefined) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["document"]),
      });
    } else if (query.code in cache["document"]) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["document"][query.code]),
      });
    } else {
      //fix loi timeout khi code document ko ton tai
      res.status(200).json({
        error: 1,
        msg: "Document not found || cache not reload",
      });
    }
  } catch (error) {
    res.status(404).json({
      error: 1,
      msg: "invalid code",
    });
  }
};
const getMistake = function (req, res, cache, common) {
  try {
    let query = req.query;
    if (query.code == undefined) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["mistake"]),
      });
    } else if (query.code in cache["mistake"]) {
      res.status(200).json({
        error: 0,
        msg: "Thanh cong",
        data: camelcaseKeys(cache["mistake"][query.code]),
      });
    }
  } catch (error) {
    res.status(404).json({
      error: 1,
      msg: "invalid code",
    });
  }
};
const getBundleDetail = function (req, res, cache, common, poolRead) {
  try {
    let query = req.query;
    console.log("req query getBundleDetail:", JSON.stringify(query));
    const systemType = req.query.systemType;
    const productType = req.query.productType;

    // if(query.role == 'CE') {
    //     if(query.code[0] !== 'S') {
    //         query.code = 'S' + query.code
    //     }
    // }
    const { code, role } = req.query;
    let params = [code, role];

    if (query.code == undefined || query.role == undefined) {
      res.status(400).json({
        error: 1,
        msg: "Bundle code or role is undefined",
      });
    } else {
      let sql =
        "select * from mapping_allcode ma where code_type  = 'BUNDLE' and value_code  = $1 and role =$2 and is_delt = 0 order by created_date";
      if (productType) {
        sql =
          "select * from mapping_allcode ma where code_type  = 'BUNDLE' and value_code  = $1 and role =$2 and product_type = $3 and is_delt = 0 order by created_date";
        params.push(productType);
      }
      poolRead.query(sql, params).then((data) => {
        if (data.rowCount > 0) {
          let listBundle = [];
          for (i in data.rows) {
            const item = cache["mistake"][data.rows[i].val_code_map];
            if (
              item == undefined ||
              (query.systemType == "VIF" && item.val_code1 == "RESUBMIT")
            ) {
            } else {
              if (query.role == "CP") {
                listBundle.push({
                  code: item.value_code || "",
                  codeName: item.value_name_vn || "",
                  codeType: item.val_code1 || "",
                });
              } else {
                listBundle.push({
                  code: item.value_code || "",
                  //codeName: item.value_code + ":" + item.value_name_vn || '',
                  codeName: item.value_name_vn || "",
                  codeType: item.val_code1 || "",
                });
              }
            }
          }
          res.status(200).json({
            error: 0,
            msg: "Thanh cong",
            data: {
              bundle: camelcaseKeys(
                cache["bundle"][query.code] != null
                  ? cache["bundle"][query.code]
                  : cache["document"][query.code]
              ),
              bundleDetails: camelcaseKeys(listBundle),
            },
          });
        } else {
          res.status(200).json({
            error: 1,
            msg: "Data is empty",
          });
        }
      });
    }
  } catch (error) {
    res.status(404).json({
      error: 1,
      msg: "invalid code",
    });
  }
};

//const data = require('./data.json')

const getBankInfo = async function (req, res, cache, common, poolWrite) {
  try {
    const { bankCode, bankBranch } = req.query;
    if (!bankCode && !bankBranch) {
      return res.status(200).json({
        code: 0,
        message: "Missing bankCode or bankBranch",
      });
    } else {
      const resultBranch = await getBankBranch(bankCode, bankBranch, poolWrite);
      const resultBank = await getBank(resultBranch.bank_code, poolWrite);

      if (resultBank && resultBranch) {
        return res.status(200).json({
          code: 1,
          message: "SUCCESS",
          data: camelcaseKeys({
            ...resultBank,
            ...resultBranch,
          }),
        });
      } else {
        return res.status(200).json({
          code: 0,
          message: "Invalid bankCode or bankBrach",
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(200).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

const getBankBranch = async (bankCode, bankBranch, poolWrite) => {
  let sql = `SELECT value_code as code, value_name_vn as name, val_code1 as ref_code,
    value_code_parent as bank_code FROM lov_allcode WHERE code_type = $1 AND `;
  let params = ["BANK_BRANCH"];
  let count = 2;
  if (bankCode) {
    sql += " value_code_parent = $" + count + " AND";
    count++;
    params.push(bankCode);
  }
  if (bankBranch) {
    sql += " value_code = $" + count + " AND";
    count++;
    params.push(bankBranch);
  }
  sql = sql.slice(0, -3);
  const result = await poolWrite.query(sql, params);
  if (result.rows) return result.rows[0];
  else return null;
};

const getBank = async (bankCode, poolWrite) => {
  let sql = `SELECT value_name_vn as bank_name  FROM lov_allcode WHERE code_type = $1 AND `;
  let params = ["BANK"];
  let count = 2;
  if (bankCode) {
    sql += " value_code = $" + count;
    count++;
    params.push(bankCode);
  }

  const result = await poolWrite.query(sql, params);
  if (result.rows) return result.rows[0];
  else return null;
};

const getMistakeList = async (req, res, cache, common, poolWrite) => {
  try {
    const { role, type } = req.query;
    if (!role || !type) {
      // Valid data
      return res.status(200).json({
        code: 0,
        message: "Missing role or type",
      });
    } else {
      const sql = `SELECT 
            la.value_code as CODE, 
            la.value_name_vn as NAME,
            la.val_code1 as TYPE
             FROM lov_allcode la JOIN mapping_allcode ma ON
            la.value_code = ma.val_code_map WHERE ma.role = $1 and la.val_code1 = $2 and ma.value_code='SCOR'`;
      const result = await poolWrite.query(sql, [role, type]);
      if (result.rows) {
        return res.status(200).json({
          code: 1,
          message: "SUCCESS",
          data: result.rows,
        });
      } else {
        return res.status(200).json({
          code: 0,
          message: "Invalid code or role",
          data: [],
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

const getBundleByDocument = async (req, res, cache, common, poolWrite) => {
  try {
    const { documentId } = req.query;
    if (!documentId) {
      // Valid data
      return res.status(200).json({
        code: 0,
        message: "Missing documentId",
      });
    } else {
      const sql = `SELECT 
            value_code as bundle_id FROM mapping_allcode  WHERE val_code_map = $1`;
      const result = await poolWrite.query(sql, [documentId]);
      if (result.rows && result.rows.length) {
        return res.status(200).json({
          code: 1,
          message: "SUCCESS",
          bundleId: result.rows[0].bundle_id,
        });
      } else {
        return res.status(200).json({
          code: 0,
          message: "Invalid documentId",
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

//const sapo = require('./sapo.json')

const getPlace = async (req, res, cache, common, poolWrite) => {
  try {
    const { code, partnercode } = req.query;
    if (!code) {
      res.status(200).json({
        code: 0,
        message: "Missing code",
      });
    } else {
      let sql = `SELECT code_type as type,value_name_vn as name
             FROM lov_allcode WHERE value_code = $1 and val_code1  `;
      if (partnercode === "SAPO") {
        sql += `= 'SAPO'`;
      } else {
        sql += `IS NULL`;
      }
      const result = await poolWrite.query(sql, [code]);
      if (result && result.rows && result.rows.length) {
        res.status(200).json({
          code: 1,
          message: "SUCCESS",
          data: result.rows[0],
        });
      } else {
        res.status(200).json({
          code: 0,
          message: "INVALID CODE",
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

const isExist = async (code, pool) => {
  sql = `SELECT * FROM lov_allcode WHERE val_code1 = 'SAPO' AND value_code = $1 LIMIT 1`;
  const result = await pool.query(sql, [code]);
  if (result && result.rows && result.rows.length) return true;
  return false;
};

const addElement = async (code, name, type, parentCode, pool) => {
  if (await isExist(code, pool)) {
    console.log(code + " is exist");
  } else {
    const sql = `INSERT INTO lov_allcode 
        (code_type,value_code,value_name_vn,value_desc,value_code_parent,val_code1)
        VALUES ($1,$2,$3,$4,$5,$6)
        `;
    const params = [type, code, name, name, parentCode, "SAPO"];
    pool.query(sql, params);
  }
};

const getMarriedStatus = async (req, res, cache, common, poolWrite) => {
  try {
    const { code } = req.query;
    if (!code) {
      res.status(200).json({
        code: 0,
        message: "Missing code",
      });
    } else {
      let sql = `select value_name_vn from lov_allcode la  where code_type = 'MARRIED_STATUS' 
            AND value_code = $1 `;

      const result = await poolWrite.query(sql, [code]);
      if (result && result.rows && result.rows.length) {
        res.status(200).json({
          code: 1,
          message: "SUCCESS",
          value: result.rows[0].value_name_vn,
        });
      } else {
        res.status(200).json({
          code: 0,
          message: "INVALID CODE",
          value: "",
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

const getValueCode = async (req, res, cache, common, poolWrite) => {
  try {
    const { code, type } = req.query;
    if (!code || !type) {
      res.status(200).json({
        code: 0,
        message: "Missing code or type",
      });
    } else {
      let sql = `select code_type as code ,
                        value_name_vn as name_vn,
                        value_name_en as name_en from lov_allcode la  where code_type = $2 
                        AND value_code = $1 `;

      const result = await poolWrite.query(sql, [code, type]);
      if (result && result.rows && result.rows.length) {
        res.status(200).json({
          code: 1,
          message: "SUCCESS",
          value: camelcaseKeys(result.rows[0]),
        });
      } else {
        res.status(200).json({
          code: 0,
          message: "INVALID CODE OR TYPE",
          value: null,
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

const getValueCodeVer2 = async (req, res, poolWrite) => {
  try {
    const { code, type } = req.query;
    if (!code || !type) {
      res.status(200).json({
        code: 0,
        message: "Missing code or type",
      });
    } else {
      let sql = `select code_type as code ,
                        value_name_vn as name_vn,
                        value_name_en as name_en,
                        val_code1 value_code1,
                        val_code2 value_code2                        
                        from lov_allcode la  where code_type = $2 
                        AND value_code = $1 `;
      const result = await poolWrite.query(sql, [code, type]);
      if (result && result.rows && result.rows.length) {
        res.status(200).json({
          code: 1,
          message: "SUCCESS",
          value: camelcaseKeys(result.rows[0]),
        });
      } else {
        res.status(200).json({
          code: 0,
          message: "INVALID CODE OR TYPE",
          value: null,
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

async function getValueCodeV2(req, res) {
  try {
    const { code, type } = req.query;
    if (!code || !type) {
      return res.status(400).json({
        code: 0,
        msg: "Missing code or type",
      });
    } else {
      const cache = global.cacheV2;
      if (cache.hasOwnProperty(type)) {
        if (cache[type].hasOwnProperty(code)) {
          return res.status(200).json({
            code: 1,
            message: "SUCCESS",
            value: camelcaseKeys(cache[type][code]),
          });
        }
      }
      return res.status(400).json({
        code: 0,
        msg: "INVALID code or type",
      });
    }
  } catch (err) {
    console.log(`get value code error : ${err.message}`);
    return res.status(500).json({
      code: -1,
      msg: "INTERNAL SERVER ERROR",
    });
  }
}

const getLovVerFullInfo = async (req, res) => {
  const poolRead = req.poolRead;
  const { codeType } = req.query;
  const sql = "select * from lov_allcode where code_type = $1";
  const queryRs = await poolRead.query(sql, [codeType]);
  if (queryRs.rowCount == 0) {
    return res.status(200).json({
      code: 0,
      msg: "Invalid codeType",
    });
  } else {
    const data = queryRs.rows.map((row) => {
      return {
        valueCode: row.value_code,
        valueVn: row.value_name_vn,
        valueEn: row.value_name_en,
        valueCodeParent: row.value_code_parent,
        valueCode1: row.val_code1,
        valueCode2: row.val_code2,
      };
    });
    return res.status(200).json({
      code: 1,
      msg: "Get lov sucessfully.",
      data,
    });
  }
};

const getPlaceInfo = async (req, res, cache, common, poolWrite) => {
  try {
    const { province, district, ward } = req.query;
    if (!code) {
      res.status(200).json({
        code: 0,
        message: "Missing code",
      });
    } else {
      let sql = `SELECT code_type as type,value_name_vn as name
             FROM lov_allcode WHERE value_code = $1 and val_code1  `;
      if (partnercode === "SAPO") {
        sql += `= 'SAPO'`;
      } else {
        sql += `IS NULL`;
      }
      const result = await poolWrite.query(sql, [code]);
      if (result && result.rows && result.rows.length) {
        res.status(200).json({
          code: 1,
          message: "SUCCESS",
          data: result.rows[0],
        });
      } else {
        res.status(200).json({
          code: 0,
          message: "INVALID CODE",
        });
      }
    }
  } catch (error) {
    console.log({ error });
    res.status(502).json({
      code: -1,
      message: "SERVER ERROR",
    });
  }
};

async function getLov(req, res) {
  const poolRead = req.poolRead;
  const { codeType } = req.query;
  const sql = "select * from lov_allcode where code_type = $1";
  const queryRs = await poolRead.query(sql, [codeType]);
  if (queryRs.rowCount == 0) {
    return res.status(200).json({
      code: 0,
      msg: "Invalid codeType",
    });
  } else {
    const data = queryRs.rows.map((row) => {
      return {
        code: row.value_code,
        value: row.value_name_vn || row.value_name_en,
      };
    });
    return res.status(200).json({
      code: 1,
      msg: "Get lov sucessfully.",
      data,
    });
  }
}

async function getLovVer2(req, res) {
  const poolRead = req.poolRead;
  const { codeType } = req.query;
  const sql = "select * from lov_allcode where code_type = $1";
  const queryRs = await poolRead.query(sql, [codeType]);
  if (queryRs.rowCount == 0) {
    return res.status(200).json({
      code: 0,
      msg: "Invalid codeType",
    });
  } else {
    const data = queryRs.rows.map((row) => {
      return {
        code: row.value_code,
        value: row.value_name_vn || row.value_name_en,
        valueCode1: row.val_code1,
        valueCode2: row.val_code2,
      };
    });
    return res.status(200).json({
      code: 1,
      msg: "Get lov sucessfully.",
      data,
    });
  }
}

async function getBankBranchLov(req, res) {
  const poolRead = req.poolRead;
  const bankCode = req.query.bankCode;
  if (bankCode == undefined) {
    return res.status(200).json({
      code: 0,
      msg: "bankCode is invalid",
    });
  }
  const sql =
    "select * from lov_allcode la where code_type ='BANK_BRANCH' and value_code_parent = $1;";
  const queryRs = await poolRead.query(sql, [bankCode]);
  const queryData = queryRs.rows;
  if (queryData.length == 0) {
    return res.status(200).json({
      code: -1,
      msg: "empty data",
      data: [],
    });
  } else {
    return res.status(200).json({
      code: 1,
      msg: "get bank branch successfully",
      data: queryData.map((x) => {
        return {
          code: x.value_code,
          name: x.value_name_vn,
        };
      }),
    });
  }
}
async function getListByCodeType(req, res) {
  const poolRead = req.poolRead;
  const { codeType, valueCode, orderIndex } = req.query;
  if (!codeType)
    return res.status(400).json({ code: 0, msg: "codeType is invalid" });

  let idx = 2;
  let params = [codeType];
  let sql = "select * from lov_allcode la where code_type = $1";
  if (valueCode) {
    sql += ` and value_code = $${idx}`;
    params.push(valueCode);
    idx++;
  }
  if (orderIndex) {
    sql += ` order by order_index ${orderIndex},value_name_vn,value_name_en`;
  } else {
    sql += " order by value_name_vn,value_name_en";
  }
  console.log(sql);
  const queryRs = await poolRead.query(sql, params);
  const queryData = queryRs.rows;
  if (queryData.length == 0)
    return res.status(200).json({
      code: 2,
      msg: "empty data",
      data: [],
    });
  return res.status(200).json({
    code: 0,
    msg: `get list ${codeType} successfully`,
    data: queryData.map((x) => {
      return {
        code: x.value_code,
        name: x.value_name_vn || x.value_name_en,
        desc: x.value_desc,
        valueCode1: x.val_code1,
        valueCode2: x.val_code2,
        valueCode3: x.val_code3,
        valueCodeParent: x.value_code_parent ?? null
      };
    }),
  });
}

async function getSmeEmploymentType4Lov(req, res) {
  const poolRead = req.poolRead;
  const smeEmploymentTypeCode = req.query.smeEmploymentTypeCode;
  if (smeEmploymentTypeCode == undefined) {
    return res.status(200).json({
      code: 0,
      msg: "smeEmploymentTypeCode is invalid",
    });
  }
  const sql =
    "select * from lov_allcode la where code_type ='SME_EMPLOYMENT_TYPE_4' and value_code_parent = $1;";
  const queryRs = await poolRead.query(sql, [smeEmploymentTypeCode]);
  const queryData = queryRs.rows;
  if (queryData.length == 0) {
    return res.status(200).json({
      code: -1,
      msg: "empty data",
      data: [],
    });
  } else {
    return res.status(200).json({
      code: 1,
      msg: "get sme employment type 4 successfully",
      data: queryData.map((x) => {
        return {
          code: x.value_code,
          name: x.value_name_vn,
        };
      }),
    });
  }
}

// get Business type
function getBusinessType(req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const businessType = cache.businessType;
  if (code == undefined) {
    const data = [];
    for (var key in businessType) {
      data.push(businessType[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get business type list successfully",
      data: data,
    });
  } else {
    if (businessType.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get business type successfully",
        data: businessType[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
}

// get Business form
function getBusinessForm(req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const businessForm = cache.businessForm;
  if (code == undefined) {
    const data = [];
    for (var key in businessForm) {
      data.push(businessForm[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get business form list successfully",
      data: data,
    });
  } else {
    if (businessForm.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get business form successfully",
        data: businessForm[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
}

// get payment method
function getPaymentMethod(req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const paymentMethod = cache.paymentMethod;
  if (code == undefined) {
    const data = [];
    for (var key in paymentMethod) {
      data.push(paymentMethod[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get payment method list successfully",
      data: data,
    });
  } else {
    if (paymentMethod.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get payment method successfully",
        data: paymentMethod[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
}

// get period
function getPeriod(req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const period = cache.period;
  if (code == undefined) {
    const data = [];
    for (var key in period) {
      data.push(period[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get payment method list successfully",
      data: data,
    });
  } else {
    if (period.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get payment method successfully",
        data: period[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
}

function getResidenceType(req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const residence_type = cache.residence_type;
  if (code == undefined) {
    const data = [];
    for (var key in residence_type) {
      data.push(residence_type[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get residence type method list successfully",
      data: data,
    });
  } else {
    if (residence_type.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get residence type successfully",
        data: residence_type[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
}

const getDocuments = function (req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const document = cache.document;
  if (code == undefined) {
    const data = [];
    for (var key in document) {
      data.push(document[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get residence type method list successfully",
      data: data,
    });
  } else {
    if (document.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get residence type successfully",
        data: document[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
};

const getCertificateType = function (req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const certificate_type = cache.certificate_type;
  if (code == undefined) {
    const data = [];
    for (var key in certificate_type) {
      data.push(certificate_type[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get certificate type method list successfully",
      data: data,
    });
  } else {
    if (certificate_type.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get certificate type successfully",
        data: certificate_type[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
};

const getAssetsType = function (req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const assets_type = cache.assets_type;
  if (code == undefined) {
    const data = [];
    for (var key in assets_type) {
      data.push(assets_type[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get assets type method list successfully",
      data: data,
    });
  } else {
    if (assets_type.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get assets type successfully",
        data: assets_type[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
};

const getPropertyType = function (req, res) {
  const cache = req.cache;
  const code = req.query.code;
  const property_type = cache.property_type;
  if (code == undefined) {
    const data = [];
    for (var key in property_type) {
      data.push(property_type[key]);
    }
    return res.status(200).json({
      code: 1,
      msg: "get property type method list successfully",
      data: data,
    });
  } else {
    if (property_type.hasOwnProperty(code)) {
      return res.status(200).json({
        code: 1,
        msg: "get property type successfully",
        data: property_type[code],
      });
    } else {
      return res.status(200).json({
        code: 0,
        msg: "invalid code",
      });
    }
  }
};

const searchValueCodeByValueName = async function (req, res) {
  try {
    const poolRead = req.poolRead;
    const body = req.body;

    const { codeType, valueName, valueCodeParent } = body;

    if (!valueName) {
      const data = {
        codeType,
        valueName,
        valueCodeParent,
        valueCode: "",
        valueNameVn: "",
      };

      return res.status(200).json({
        code: 0,
        message: "searchValueCodeByValueName success",
        data,
      });
    }

    let [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
      poolRead,
      codeType,
      `%${valueName}`,
      valueCodeParent
    );
    if (!valueCode || !valueNameVn) {
      let match1 = valueName.match(/^(phường|quận) (\d)$/i);
      if (match1) {
        [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
          poolRead,
          codeType,
          `%${match1[1]} 0${match1[2]}`,
          valueCodeParent
        );
      }

      let match2 = valueName.match(/^(phường|quận) 0(\d)$/i);
      if (match2) {
        [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
          poolRead,
          codeType,
          `%${match2[1]} ${match2[2]}`,
          valueCodeParent
        );
      }

      if (!valueCode || !valueNameVn) {
        let match3 = valueName.match(/òa/i);
        if (match3) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("òa").join("oà")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        let match4 = valueName.match(/oà/i);
        if (match4) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("oà").join("òa")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        let match5 = valueName.match(/ụy/i);
        if (match5) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("ụy").join("uỵ")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        let match6 = valueName.match(/uỵ/i);
        if (match6) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("uỵ").join("ụy")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        let match7 = valueName.match(/ủy/i);
        if (match7) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("ủy").join("uỷ")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        let match8 = valueName.match(/Đak/i);
        if (match8) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("Đak").join("Đăk")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        let match8 = valueName.match(/uỷ/i);
        if (match8) {
          [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
            poolRead,
            codeType,
            `%${valueName.split("uỷ").join("ủy")}`,
            valueCodeParent
          );
        }
      }

      if (!valueCode || !valueNameVn) {
        [valueCode, valueNameVn] = await doSearchValueCodeByValueName(
          poolRead,
          codeType,
          `%${valueName}%`,
          valueCodeParent
        );
      }
    }
    const data = {
      codeType,
      valueName,
      valueCodeParent,
      valueCode,
      valueNameVn,
    };

    res.status(200).json({
      code: 0,
      message: "searchValueCodeByValueName success",
      data,
    });
  } catch (err) {
    console.log(err);

    res.status(400).json({
      code: -1,
      message: err.message,
    });
  }
};

const doSearchValueCodeByValueName = async function (
  poolRead,
  codeType,
  valueSearch,
  valueCodeParent
) {
  try {
    const params = [codeType, valueSearch];
    let valueCodeParentQuery = "";
    if (valueCodeParent) {
      params.push(valueCodeParent);
      valueCodeParentQuery = `and value_code_parent = $${params.length}`;
    }
    const query = `select value_code, value_name_vn, value_code_parent from lov_allcode where code_type = $1 and unaccent(value_name_vn) ilike unaccent($2) ${valueCodeParentQuery}`;

    const result = await poolRead.query(query, params);

    let valueCode = "";
    let valueNameVn = "";
    if (result && result.rows.length) {
      valueCode = result.rows[0].value_code;
      valueNameVn = result.rows[0].value_name_vn;
    }

    return [valueCode, valueNameVn];
  } catch (err) {
    console.log(err);

    return ["", ""];
  }
};

const standardAddressString = (address) => {
  const listAddress = address.split(",");
  const provinceStr = listAddress
    .splice(listAddress.length - 1)
    .join(",")
    .trim()
    .replace("TP.", "Thành phố ")
    .replace(/ {2}/g, " ");
  let districtStr = listAddress
    .splice(listAddress.length - 1)
    .join(",")
    .trim()
  if (!districtStr) {
    districtStr = provinceStr
  }

  if (districtStr.startsWith("Q. ")) {
    districtStr = districtStr.replace("Q. PN", "Quận Phú Nhuận");
    districtStr = districtStr.replace("Q. TB", "Quận Tân Bình");
    districtStr = districtStr.replace("Q. TP", "Quận Tân Phú");
    districtStr = districtStr.replace("Q. BT", "Quận Bình Thạnh");
    districtStr = districtStr.replace("Q. GV", "Quận Gò Vấp");
  }

  districtStr = districtStr.replace("TP.", "Thành phố ")
    .replace("Q.", "Quận ")
    .replace("H.", "Huyện ")
    .replace(/ {2}/g, " ");


  let wardStr = listAddress
    .splice(listAddress.length - 1)
    .join(",")
    .trim()
  if (!wardStr) {
    wardStr = districtStr
  }
  wardStr = wardStr.replace("P.", "Phường ")
    .replace("TT.", "Thị trấn ")
    .replace(/ {2}/g, " ");
  const detailAddress = listAddress.splice(0).join(",").trim();

  return { provinceStr, districtStr, wardStr, detailAddress };
};
const getAddressCodeByAddress = async function (req, res) {
  try {
    const poolRead = req.poolRead;
    const body = req.body;

    const { address } = body;

    const { provinceStr, districtStr, wardStr, detailAddress } =
      standardAddressString(address);

    const data = {
      address,
      provinceStr,
      districtStr,
      wardStr,
      provinceCode: "",
      districtCode: "",
      wardCode: "",
      detailAddress: detailAddress,
    };

    if (!address) {
      return res.status(200).json({
        code: 0,
        message: "getAddressCodeByAddress success",
        data,
      });
    }
    const { provinceCode, districtCode, wardCode, ward, district, province } =
      await mappingAddressCode(poolRead, provinceStr, districtStr, wardStr);

    data.provinceCode = provinceCode;
    data.districtCode = districtCode;
    data.wardCode = wardCode;
    data.ward = ward;
    data.district = district;
    data.province = province;

    return res.status(200).json({
      code: 0,
      message: "getAddressCodeByAddress success",
      data,
    });
  } catch (err) {
    console.log(err);

    res.status(400).json({
      code: -1,
      message: err.message,
    });
  }
};

const getAddressCodeByAddresses = async function (req, res) {
  try {
    const poolRead = req.poolRead;
    const body = req.body;

    const { addresses } = body;

    const result = [];
    for (const address of addresses) {
      const { provinceStr, districtStr, wardStr, detailAddress } =
        standardAddressString(address);

      const data = {
        address,
        provinceStr,
        provinceValueName: "",
        provinceCode: "",
        districtStr,
        districtValueName: "",
        districtCode: "",
        wardStr,
        wardValueName: "",
        wardCode: "",
        detailAddress: detailAddress,
      };

      if (!address) {
        return res.status(200).json({
          code: 0,
          message: "getAddressCodeByAddress success",
          data,
        });
      }
      const { provinceCode, districtCode, wardCode, ward, district, province } =
        await mappingAddressCode(poolRead, provinceStr, districtStr, wardStr);
      data.ward = ward;
      data.district = district;
      data.province = province;
      data.wardCode = wardCode;
      data.districtCode = districtCode;
      data.provinceCode = provinceCode;
      data.wardValueName = ward?.value_name_vn;
      data.districtValueName = district?.value_name_vn;
      data.provinceValueName = province?.value_name_vn;
      result.push(data);
    }

    return res.status(200).json({
      code: 0,
      message: "getAddressCodeByAddresses success",
      data: result,
    });
  } catch (err) {
    console.log(err);

    res.status(400).json({
      code: -1,
      message: err.message,
    });
  }
};

const mappingAddressCode = async (
  poolRead,
  provinceStr,
  districtStr,
  wardStr
) => {
  const [listProvinceNearest, [valueCodeProvince, valueNameVnProvince]] =
    await Promise.all([
      getListValueCodeNeareast(poolRead, "PROVINCE", provinceStr, 2),
      doSearchValueCodeByValueName(poolRead, "PROVINCE", `%${provinceStr}`),
    ]);

  const data = {
    provinceCode: "",
    districtCode: "",
    wardCode: "",
  };
  for (const province of listProvinceNearest) {
    if (valueCodeProvince) {
      data.provinceCode = valueCodeProvince;
      data.province = {
        value_code: valueCodeProvince,
        value_name_vn: valueNameVnProvince,
      };
    } else {
      data.provinceCode = province.value_code;
      data.province = province;
    }

    const listDistrictChild = await getListValueCodeNeareast(
      poolRead,
      "DISTRICT",
      districtStr,
      30,
      data.provinceCode
    );
    if (!listDistrictChild.length) {
      continue;
    }
    listDistrictChild.sort((a, b) => {
      return (
        b.count - a.count ||
        levenshteinDistance(districtStr, a.value_name_vn) -
        levenshteinDistance(districtStr, b.value_name_vn)
      );
    });
    for (const district of listDistrictChild) {
      if (+district.count > district.value_name_vn.split(" ").length) {
        continue;
      }
      const matchedInput = districtStr.match(/^(phường|quận) (\d+)$/i);
      const matchedDb = district.value_name_vn.match(/^(phường|quận) (\d+)$/i);
      if (
        matchedInput &&
        matchedDb &&
        Number(matchedInput?.[2]) !== Number(matchedDb?.[2])
      ) {
        continue;
      }

      data.districtCode = district.value_code;
      data.district = district;
      const listWardChild = await getListValueCodeNeareast(
        poolRead,
        "WARD",
        wardStr,
        50,
        data.districtCode
      );
      if (!listWardChild.length) {
        continue;
      }
      listWardChild.sort((a, b) => {
        return (
          b.count - a.count ||
          levenshteinDistance(wardStr, a.value_name_vn) -
          levenshteinDistance(wardStr, b.value_name_vn)
        );
      });
      for (const ward of listWardChild) {
        if (+ward.count > ward.value_name_vn.split(" ").length) {
          continue;
        }
        const matchedInput = wardStr.match(/^(phường|quận) (\d+)$/i);
        const matchedDb = ward.value_name_vn.match(/^(phường|quận) (\d+)$/i);
        if (
          matchedInput &&
          matchedDb &&
          Number(matchedInput?.[2]) !== Number(matchedDb?.[2])
        ) {
          continue;
        }
        data.wardCode = ward.value_code;
        data.ward = ward;
        if (data.provinceCode && data.districtCode && data.wardCode) {
          return data;
        }
      }
      data.wardCode = listWardChild[0].value_code;
      data.ward = listWardChild[0];
    }
    data.districtCode = listDistrictChild[0].value_code;
    data.district = listDistrictChild[0];
  }
  return data;
};

const getListValueCodeNeareast = async function (
  poolRead,
  codeType,
  valueSearch,
  limit,
  valueCodeParent
) {
  try {
    const params = [codeType, valueSearch];
    let limitQuery = "";
    if (limit) {
      limitQuery = `limit ${limit}`;
    }
    let valueCodeParentQuery = "";
    if (valueCodeParent) {
      params.push(valueCodeParent);
      valueCodeParentQuery = ` and value_code_parent = $${params.length} `;
    }
    const countQuery = `SELECT COUNT(*)
        FROM UNNEST(STRING_TO_ARRAY($2, ' ')) AS word
        WHERE unaccent(value_name_vn) ILIKE '%' || unaccent(word) || '%'`;
    const query = `select value_code,value_name_vn,value_code_parent, (${countQuery}) as count from lov_allcode 
        where code_type = $1 ${valueCodeParentQuery}
        and  (${countQuery}) > 0
        ORDER BY (${countQuery}) desc ${limitQuery}`;
    const result = await poolRead.query(query, params);

    return result.rows;
  } catch (err) {
    console.log(err);

    return [];
  }
};

const mappingSmeKeys = async (obj, partnerCode) => {
  let cached = {};
  let configs = await getSmeConfigKey();
  // keyMappings = keyMappings.map(e=> e.key);
  async function recurse(currentObj, partnerCode, configKeys) {
    const keyMappings = configKeys.map((e) => e.key);
    if (Array.isArray(currentObj)) {
      // Nếu là mảng, lặp qua từng phần tử của mảng
      for (let item of currentObj) {
        if (typeof item === "object" && item !== null) {
          // Nếu phần tử là object, đệ quy tiếp
          await recurse(item, partnerCode, configKeys);
        }
      }
    } else if (typeof currentObj === "object" && currentObj !== null) {
      for (let key in currentObj) {
        if (
          keyMappings.includes(key) &&
          typeof currentObj[key] === "string" &&
          !helper.isNullOrEmpty(currentObj[key])
        ) {
          const config = configKeys.find((e) => e.key === key);
          const valueCode = currentObj[key];
          if (!config) continue;
          else if (config.mapping_type === "lov") {
            if (!helper.isNullOrEmpty(cached[key]?.[valueCode])) {
              currentObj[key] = cached[key][valueCode];
            } else {
              const value = (
                await getSmeMappingCode({ partnerCode, key, valueCode })
              )?.ref_code;
              if (!helper.isNullOrEmpty(value)) {
                if (!helper.isNullOrEmpty(cached[key]?.[valueCode])) {
                  currentObj[key] = cached[key][valueCode];
                }
                currentObj[key] = value;
                cached[key] = cached[key] ?? {};
                cached[key][valueCode] = value;
              }
            }
          } else {
            const value = (
              await getSmeMappingCode({ partnerCode, key, valueCode })
            )?.ref_code;
            if (!helper.isNullOrEmpty(value)) {
              currentObj[key] = value;
            }
          }
        } else if (
          typeof currentObj[key] === "object" &&
          currentObj[key] !== null
        ) {
          await recurse(currentObj[key], partnerCode, configKeys);
        }
      }
    }
    return obj;
  }

  // Bắt đầu đệ quy với object ban đầu
  obj = await recurse(obj, partnerCode, configs);
  return obj;
};

const mappingCodeSme = async (req, res) => {
  try {
    const { partnerCode } = req.query;
    const body = req.body ?? {};
    if (!partnerCode) {
      return res.status(400).json({
        code: 0,
        msg: "Missing partnerCode",
      });
    }
    // const data = await getSmeMappingCode({codeType, partnerCode, codeValue});
    await mappingSmeKeys(body, partnerCode);

    return res.status(200).json({
      code: 1,
      msg: "Get lov sucessfully.",
      data: body,
    });
  } catch (error) {
    return res.status(500).json({
      code: 0,
      msg: "Internal Server Error",
      details: error?.message ?? "",
    });
  }
};

module.exports = {
  getBusinessUnit,
  getProvince,
  getDistrict,
  getWard,
  getEmploymentType,
  getRefType,
  getBundle,
  getDocument,
  getMistake,
  getBundleDetail,
  getBankInfo,
  getMistakeList,
  getBundleByDocument,
  getPlace,
  getMarriedStatus,
  getValueCode,
  getPlaceInfo,
  getLov,
  getBankBranchLov,
  getListByCodeType,
  getValueCodeV2,
  getSmeEmploymentType4Lov,
  // importData
  getBusinessType,
  getBusinessForm,
  getPaymentMethod,
  getPeriod,
  getResidenceType,
  getDocuments,
  getCertificateType,
  getAssetsType,
  getPropertyType,
  getLovVer2,
  getValueCodeVer2,
  getLovVerFullInfo,
  searchValueCodeByValueName,
  getAddressCodeByAddress,
  getAddressCodeByAddresses,
  mappingCodeSme,
};
