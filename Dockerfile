#FROM 446567516155.dkr.ecr.ap-southeast-1.amazonaws.com/docker-image-common-alpine
FROM els-registry.evnfc.vn/common/docker-image-common-alpine@sha256:278302d1a4ed89bf9342fa1326f6f3a9ac97e133516492c22a45c91cb9e2d425


# Create app directory
WORKDIR /usr/src/app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
COPY package*.json ./

#RUN npm install
RUN npm ci --only=production

# Bundle app source
COPY . .

# EXPOSE 1000

CMD [ "node", "main.js" ]
 
 
