function getDocument(req,res,poolRead) {
	const sql = 'select value_code as doc_name,value_name_en as doc_en,value_name_vn as doc_vn from lov_allcode la2 where value_code_parent =$1';
	const bundleCode = req.query.bundlecode
	poolRead.query(sql,[bundleCode])
	.then(result => {
		if ( result == undefined || result.rows.length == 0) {
			return res.status(404).json({
				code : -1,
				msg : "invalid bundle code"
 			})
		}
		else {
			return res.status(200).json({
				code : 1,
				msg : "get bundle success",
				data : result.rows
			})
		}
	})

}

module.exports = {
	getDocument
}