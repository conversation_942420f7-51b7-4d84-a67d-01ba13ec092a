function getBundleByDocument(req,res,poolRead) {
	const docName = req.query.documentId

	const sql = "select value_code_parent from lov_allcode la where value_code =$1"
	poolRead.query(sql,[docName])
	.then(result => {
		if(result === undefined || result.rows.length === 0) {
			return res.status(400).json({
				code : -1,
				msg : "can not find bundle with doc name " + docName
			})
		}
		else {
			const bundleName = result.rows[0].value_code_parent
			if(bundleName == '') {
				return res.status(200).json({
					code : 0,
					msg : "document have no bundle name",
				})
			}
			else {
				return res.status(200).json({
					code : 1,
					msg : "get bundle successful",
					bundleName : bundleName
				})
			}
		}
	})
	.catch(error => {
		return res.status(200).json({
			code : -1,
			msg : "service error"
		})
	})
}


module.exports = {
	getBundleByDocument
}
