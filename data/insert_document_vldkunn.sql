-- Insert script for DOCUMENT type VLDKUNN
-- <PERSON><PERSON><PERSON><PERSON> đề nghị giải ngân kiêm KUNN

-- Check if record already exists before inserting
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM public.lov_allcode 
        WHERE code_type = 'DOCUMENT' AND value_code = 'VLDKUNN'
    ) THEN
        INSERT INTO public.lov_allcode 
        (code_type, value_code, value_name_vn, value_name_en, value_desc, value_code_parent, val_code1, val_code2, val_code3, is_delt, created_date, updated_date, created_user, owner_id) 
        VALUES (
            'DOCUMENT',
            'VLDKUNN',
            'Gi<PERSON>y đề nghị giải ngân kiêm KUNN',
            'Giấy đề nghị giải ngân kiêm KUNN',
            'Gi<PERSON>y đề nghị giải ngân kiêm KUNN',
            null,
            null,
            null,
            null,
            0,
            NOW(),
            NOW(),
            'system',
            1
        );
        
        RAISE NOTICE 'Successfully inserted DOCUMENT VLDKUNN record';
    ELSE
        RAISE NOTICE 'Record with code_type=DOCUMENT and value_code=VLDKUNN already exists, skipping insert';
    END IF;
END $$;

-- Verify the insert
SELECT 
    id,
    code_type,
    value_code,
    value_name_vn,
    value_name_en,
    created_date,
    created_user
FROM public.lov_allcode 
WHERE code_type = 'DOCUMENT' AND value_code = 'VLDKUNN';
