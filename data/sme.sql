CREATE TABLE public.sme_mapping (
	id serial4 NOT NULL,
    partner_code varchar(100) NULL,
	code_type varchar(100) NULL,
	value_code varchar(100) NULL,
	ref_type varchar(100) NULL,
	ref_code varchar(100) NULL,
	
	is_delt int4 DEFAULT 0 NULL,
	created_date timestamp DEFAULT now() NULL,
	updated_date timestamp NULL,
	created_user varchar(100) NULL,

	CONSTRAINT sme_mapping_pk PRIMARY KEY (id)
);
CREATE INDEX sme_mapping_partner_code_idx ON public.sme_mapping USING btree (partner_code);
CREATE INDEX sme_mapping_code_type_idx ON public.sme_mapping USING btree (code_type);
CREATE INDEX sme_mapping_value_code_idx ON public.sme_mapping USING btree (value_code);
CREATE INDEX sme_mapping_ref_type_idx ON public.sme_mapping USING btree (ref_type);
CREATE INDEX sme_mapping_ref_code_idx ON public.sme_mapping USING btree (ref_code);