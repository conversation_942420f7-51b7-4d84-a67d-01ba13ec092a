const common = require("../common")

function getDocMistake(req,res) {
    const {role,code} = req.query
    if(role == undefined || code == undefined) {
        return res.status(201).json({
            code : -1,
            msg : "role or code is missing."
        })
    }

    if(role == CE) {
        const url = req.config.basic.bus.api + `/masterdata/v1/bundle/detail?role=${role}&code=${code}`

    }
}