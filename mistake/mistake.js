async function getMistakeList(req,res) {
    const poolRead = req.poolRead
    const {role,type} = req.query
    const sql = "select * from lov_allcode la where code_type ='MISTAKE' and value_code_parent =$1 and val_code1 =$2;"
    const queryRs = await poolRead.query(sql,[type,role])
    if(queryRs.rows.length == 0) {
        return res.status(200).json({
            code : 0,
            msg : "khong tim thay du lieu."
        })    
    }
    if(type == 'CHECKDOCS_REASON') {
        const resultDict = {}
        
        queryRs.rows.forEach(element => {
            if(!resultDict.hasOwnProperty(element.value_code)) {
                resultDict[element.value_code] = {}
                resultDict[element.value_code].decision = 1
                resultDict[element.value_code].code = element.value_code

                resultDict[element.value_code].value = []
            }
            resultDict[element.value_code].value.push(element.value_name_en)
        });

        const resultList = []
        for(var key in resultDict) {
            resultList.push(resultDict[key])
        }
        
        return res.status(200).json({
            code : 1,
            msg : "lay du lieu thanh cong.",
            data : resultList
        })
    }

    if(type == 'MANUAL_DECS_LOV') {

        const resultDict = {}
        
        queryRs.rows.forEach(element => {
            if(!resultDict.hasOwnProperty(element.val_code3)) {
                resultDict[element.val_code3] = {}
                resultDict[element.val_code3].act = parseInt(element.val_code3)
                resultDict[element.val_code3].abbrev = element.value_code
                resultDict[element.val_code3].decision = element.value_name_en
                resultDict[element.val_code3].codeDecison = []
            }
            resultDict[element.val_code3].codeDecison.push(element.val_code2)
        });

        const resultList = []
        for(var key in resultDict) {
            resultList.push(resultDict[key])
        }
        return res.status(200).json({
            code : 1,
            msg : "lay du lieu thanh cong.",
            data : resultList
        })
    }

    if(type == 'KYC_REJECT') {
        return res.status(200).json({
            code : 1,
            msg : "lay du lieu thanh cong.",
            data : queryRs.rows.map(row => {
                return {
                    code : row.value_code + " : " + row.value_name_en,
                    value : row.value_name_en
                }
            })
        })
    }

    return res.status(200).json({
        code : 1,
        msg : "lay du lieu thanh cong.",
        data : queryRs.rows.map(row => {
            return {
                code : row.value_code,
                value : row.value_name_en
            }
        })
    })
}

module.exports = {
    getMistakeList
}