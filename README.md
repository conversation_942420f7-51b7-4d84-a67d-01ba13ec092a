# MASTERDATA-SERVICE
Masterdata-service là service được sử dụng lưu trữ quản lý data dùng chung cho toàn hệ thống core.

# Getting Started
1. API health check service
````
GET: /masterdata/v1/healthcheck
````
2. API get all cache địa bàn
```
GET: /masterdata/v1/cache
```
3. API lấy thông tin Tỉnh/Thành Phố
```
GET: /masterdata/v1/provinces?code=06
```
Trong đó:
* code: Là mã Tỉnh/TP
ö
4. API lấy thông tin Quận/Huyện
```
GET: /masterdata/v1/districts?code=065
```
Trong đó:
* code: Là mã Quận/Huyện

4. API lấy thông tin Xã/Phường
```
GET: /masterdata/v1/wards?code=02086
```
Trong đó:
* code: Là mã Xã/Phường

# Build with
Masterdata-service được build trên nodejs.

# Vesioning
Version 1.0

# ZERO

# Author: 
* Lê Thanh Tùng
* Email: <EMAIL>
* Contributors: N/A

# License
This project is belong to ENVFC
   
