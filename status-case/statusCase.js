function getListStepName(req,res) {
    const poolRead = req.poolRead
	const sql = 'select code_type, value_code, value_name_en, value_desc from lov_allcode la where code_type  = $1 and value_code_parent is null';
	const codeType = req.query.codeType
	poolRead.query(sql,[codeType])
	.then(result => {
		if ( result == undefined || result.rows.length == 0) {
			return res.status(404).json({
				code : -1,
				msg : "data not found"
 			})
		}
		else {
			return res.status(200).json({
				code : 1,
				msg : `get code type ${codeType} success`,
				data : result.rows
			})
		}
	})
}

function getListAction(req,res) {
    const poolRead = req.poolRead
	let sql = 'select code_type, value_code, value_name_en, value_desc from lov_allcode la where code_type  = $1 and value_code_parent =$2';
    let payload = req.query

	poolRead.query(sql,[payload.codeType,payload.stepName])
	.then(result => {
		if ( result == undefined || result.rows.length == 0) {
			return res.status(404).json({
				code : -1,
				msg : "data not found"
 			})
		}
		else {
			return res.status(200).json({
				code : 1,
				msg : `get get list action success`,
				data : result.rows
			})
		}
	})
}
function getCaseStatusValue(req,res) {
    const poolRead = req.poolRead
	let sql = 'select code_type, value_code, value_name_en, value_desc, val_code1, val_code2, val_code3 from lov_allcode la where code_type  = $1 and value_code=$2';
    let payload = req.query
    let params = [payload.codeType,payload.valueCode]
    if(payload.stepCode){
        sql += ' and value_code_parent = $3'
        params.push(payload.stepCode)
    }else{
        sql += ' and value_code_parent is null'
    }

	poolRead.query(sql,params)
	.then(result => {
		if ( result == undefined || result.rows.length == 0) {
			return res.status(404).json({
				code : -1,
				msg : "data not found"
 			})
		}
		else {
			return res.status(200).json({
				code : 1,
				msg : `get get list action success`,
				data : result.rows
			})
		}
	})
}
function getListAction(req,res) {
    const poolRead = req.poolRead
	let sql = 'select code_type, value_code, value_name_en, value_desc from lov_allcode la where code_type  = $1 and value_code_parent =$2';
    let payload = req.query

	poolRead.query(sql,[payload.codeType,payload.stepName])
	.then(result => {
		if ( result == undefined || result.rows.length == 0) {
			return res.status(404).json({
				code : -1,
				msg : "data not found"
 			})
		}
		else {
			return res.status(200).json({
				code : 1,
				msg : `get get list action success`,
				data : result.rows
			})
		}
	})
}
function getFullCaseStatus(req,res) {
    const poolRead = req.poolRead
	let sql = 'select * from lov_allcode la where code_type  = $1';
    let payload = req.query
	poolRead.query(sql,[payload.codeType])
	.then(result => {
		if ( result == undefined || result.rows.length == 0) {
			return res.status(404).json({
				code : -1,
				msg : "data not found"
 			})
		}
		else {
			let listAllCaseStatus = result.rows
			let data = {}
			listAllCaseStatus.forEach(element => {
				if(!element.value_code_parent){
					data[element.value_code] = element
				}
			});
			listAllCaseStatus.forEach(element => {
				if(element.value_code_parent){
					data[element.value_code_parent][element.value_code] = element
				}
			});
			return res.status(200).json({
				code : 1,
				msg : `get get list action success`,
				data : data
			})
		}
	})
}

module.exports = {
	getListStepName,
    getListAction,
    getCaseStatusValue,
	getFullCaseStatus
}