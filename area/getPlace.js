const common = require("../common");

async function getProvince(req, res) {
  const partnerCode = req.query.partnerCode;
  let sql =
    "select * from lov_allcode la where code_type ='PROVINCE' and val_code1 = 'SAPO' order by value_name_vn";
  if (partnerCode === undefined) {
    sql =
      "select * from lov_allcode la where code_type ='PROVINCE' and val_code1 is null order by value_name_vn";
  }
  const placeResult = await req.poolRead.query(sql);
  return res.status(200).json({
    code: 1,
    msg: "get province list successfully.",
    data: placeResult.rows.map((row) => {
      return {
        provinceCode: row.value_code,
        provinceName: row.value_name_vn,
      };
    }),
  });
}

async function getNewProvinces(req, res) {
  const sql =
    "select * from lov_allcode la where code_type ='NEW_PROVINCE' order by value_code asc";
  const placeResult = await req.poolRead.query(sql);
  return res.status(200).json({
    code: 1,
    msg: "Successfully",
    data: placeResult.rows.map((row) => {
      return {
        provinceCode: row.value_code,
        provinceName: row.value_name_vn,
      };
    }),
  });
}

async function getCommunes(req, res) {
  const provinceCode = req.query.provinceCode;
  if(!provinceCode) {
    return res.status(400).json({
      code: 0,
      msg: "Province code is required",
    });
  }
  const sql = "select * from lov_allcode la where code_type ='NEW_WARD' and value_code_parent = $1 order by value_code asc";
  const placeResult = await req.poolRead.query(sql, [provinceCode]);
  return res.status(200).json({
    code: 1,
    msg: "Successfully",
    data: placeResult.rows.map((row) => {
      return {
        communeCode: row.value_code,
        communeName: row.value_name_vn,
      };
    }),
  });
}

async function getNewWards(req, res) {
  const code = req.query.province;
  if(!code) {
    return res.status(400).json({
      code: 0,
      msg: "Province code is required",
    });
  }
  const sql = "select * from lov_allcode la where code_type ='NEW_WARD' and value_code_parent = $1 order by value_code asc";
  const placeResult = await req.poolRead.query(sql, [code]);
  return res.status(200).json({
    code: 1,
    msg: "Successfully",
    data: placeResult.rows.map((row) => {
      return {
        wardCode: row.value_code,
        wardName: row.value_name_vn,
      };
    }),
  });
}

async function getDistrict(req, res) {
  const { provinceCode, partnerCode } = req.query;
  if (provinceCode == undefined) {
    let sql =
      "select * from lov_allcode la where code_type ='DISTRICT'  and val_code1 is null order by value_name_vn";
    if (partnerCode === "SAPO") {
      sql =
        "select * from lov_allcode la where code_type ='DISTRICT' and val_code1 ='SAPO' order by value_name_vn";
    }
    const placeResult = await req.poolRead.query(sql);
    return res.status(200).json({
      code: 1,
      msg: "get district list successfully.",
      data: placeResult.rows.map((row) => {
        return {
          districtCode: row.value_code,
          districtName: row.value_name_vn,
        };
      }),
    });
  } else {
    let sql =
      "select * from lov_allcode la where code_type ='DISTRICT' and value_code_parent = $1 and val_code1 is null order by value_name_vn";
    if (partnerCode === "SAPO") {
      sql =
        "select * from lov_allcode la where code_type ='DISTRICT' and value_code_parent = $1 and val_code1 ='SAPO' order by value_name_vn";
    }
    const placeResult = await req.poolRead.query(sql, [provinceCode]);
    return res.status(200).json({
      code: 1,
      msg: "get district info successfully.",
      provinceCode: provinceCode,
      districts: placeResult.rows.map((row) => {
        return {
          districtCode: row.value_code,
          districtName: row.value_name_vn,
        };
      }),
    });
  }
}

async function getWard(req, res) {
  const { districtCode, partnerCode } = req.query;
  if (districtCode == undefined) {
    let sql =
      "select * from lov_allcode la where code_type ='WARD' order by value_name_vn";

    const placeResult = await req.poolRead.query(sql);
    return res.status(200).json({
      code: 1,
      msg: "get wards list successfully.",
      data: placeResult.rows.map((row) => {
        return {
          wardCode: row.value_code,
          wardName: row.value_name_vn,
        };
      }),
    });
  } else {
    let sql =
      "select * from lov_allcode la where code_type ='WARD' and value_code_parent = $1 order by value_name_vn";

    const placeResult = await req.poolRead.query(sql, [districtCode]);
    return res.status(200).json({
      code: 1,
      msg: "get wards info successfully.",
      districtCode: districtCode,
      wards: placeResult.rows.map((row) => {
        return {
          wardCode: row.value_code,
          wardName: row.value_name_vn,
        };
      }),
    });
  }
}

/**
 * Func get thong tin tinh/thành, quận/huyện, xã/phường
 * input: { provinceCode: "", districtCode: "", wardCode: "" }
 * output: { provinceName: "", districtName: "", wardName: "" }
 */

const getAddress = async function (req, res) {
  try {
    const poolRead = req.poolRead;
    const body = req.body;

    const provinceCode = body.provinceCode,
      districtCode = body.districtCode,
      wardCode = body.wardCode;

    var provinceQuery =
      "select value_name_vn from lov_allcode where code_type = 'PROVINCE' and value_code = $1 and val_code1 is null";
    var districtQuery =
      "select value_name_vn from lov_allcode where code_type = 'DISTRICT' and value_code = $1 and val_code1 is null";
    var wardQuery =
      "select value_name_vn from lov_allcode where code_type = 'WARD' and value_code = $1 and value_code_parent = $2";

    var listQuery = [
      poolRead.query(provinceQuery, [provinceCode]),
      poolRead.query(districtQuery, [districtCode]),
      poolRead.query(wardQuery, [wardCode, districtCode]),
    ];

    var result = await Promise.all(listQuery).catch((err) => {
      common.log("Get address error");
      console.log(err);
    });

    var provinceName, districtName, wardName;
    if (result !== undefined) {
      provinceName =
        result[0].rowCount > 0 ? result[0].rows[0].value_name_vn : "";
      districtName =
        result[1].rowCount > 0 ? result[1].rows[0].value_name_vn : "";
      wardName = result[2].rowCount > 0 ? result[2].rows[0].value_name_vn : "";
    }
    var kq = {
      provinceName: provinceName,
      districtName: districtName,
      wardName: wardName,
    };

    res.status(200).json({
      code: 0,
      message: "Get address success",
      data: kq,
    });
  } catch (err) {
    common.log("Get address error");
    console.log(err);

    res.status(400).json({
      code: -1,
      message: err.message,
    });
  }
};

const getAddressV2 = async function (req, res) {
  try {
    const poolRead = req.poolRead;
    const body = req.body;

    const provinceCode = body.provinceCode,
      districtCode = body.districtCode,
      wardCode = body.wardCode,
      newProvinceCode = body.newProvinceCode,
      newWardCode = body.newWardCode;

    const provinceQuery =
      "select value_name_vn from lov_allcode where code_type = 'PROVINCE' and value_code = $1 and val_code1 is null";
    const districtQuery =
      "select value_name_vn from lov_allcode where code_type = 'DISTRICT' and value_code = $1 and val_code1 is null";
    const wardQuery =
      "select value_name_vn from lov_allcode where code_type = 'WARD' and value_code = $1 and value_code_parent = $2";
    const newProvinceQuery =
      "select value_name_vn from lov_allcode where code_type = 'NEW_PROVINCE' and value_code = $1";
    const newCommuneQuery =
      "select value_name_vn from lov_allcode where code_type = 'NEW_WARD' and value_code = $1 and value_code_parent = $2";
    var listQuery = [
      poolRead.query(provinceQuery, [provinceCode]),
      poolRead.query(districtQuery, [districtCode]),
      poolRead.query(wardQuery, [wardCode, districtCode]),
      poolRead.query(newProvinceQuery, [newProvinceCode]),
      poolRead.query(newCommuneQuery, [newWardCode, newProvinceCode]),
    ];

    var result = await Promise.all(listQuery).catch((err) => {
      common.log("Get address error");
      console.log(err);
    });

    var provinceName, districtName, wardName, newProvinceName, newWardName;
    if (result !== undefined) {
      provinceName =
        result[0].rowCount > 0 ? result[0].rows[0].value_name_vn : "";
      districtName =
        result[1].rowCount > 0 ? result[1].rows[0].value_name_vn : "";
      wardName = result[2].rowCount > 0 ? result[2].rows[0].value_name_vn : "";
      newProvinceName =
        result[3].rowCount > 0 ? result[3].rows[0].value_name_vn : "";
      newWardName =
        result[4].rowCount > 0 ? result[4].rows[0].value_name_vn : "";
    }
    var kq = {
      provinceName: provinceName,
      districtName: districtName,
      wardName: wardName,
      newProvinceName: newProvinceName,
      newWardName: newWardName,
    };

    res.status(200).json({
      code: 0,
      message: "Get address success",
      data: kq,
    });
  } catch (err) {
    common.log("Get address error");
    console.log(err);

    res.status(400).json({
      code: -1,
      message: err.message,
    });
  }
};

module.exports = {
  getProvince,
  getDistrict,
  getWard,
  getAddress,
  getNewProvinces,
  getCommunes,
  getNewWards,
  getAddressV2
};
