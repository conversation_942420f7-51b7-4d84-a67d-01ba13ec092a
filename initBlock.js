/**
 * Ham load du lieu database to cache
 * @param {*} poolRead 
 */

const loadCache = function (poolRead) {
    return new Promise(function (resolve, reject) {
        try {
            let sql = 'select	code_type , value_code , value_name_vn , value_name_en , value_desc , value_code_parent , val_code1 , val_code2 , val_code3 from lov_allcode where is_delt = 0';
            let temp = {} // doi tuong cache
            let area = {}// Dia ban
            let status = -2;
            let employmentType = {}
            let refType = {}
            let bundle = {}
            let document = {}
            let mistake = {}
            const province = {}
            const district = {}
            const ward = {}
            const jobType = {}
            const profession = {}
            const employmentTypeFull = {}
            const businessType = {}
            const businessForm = {}
            const paymentMethod = {}
            const period = {}
            const residence_type = {}
            const certificate_type = {}
            const assets_type = {}
            const property_type = {}
            poolRead.query(sql, [], (error, result) => {
                if (error) {
                    status = -2 // Khog ket noi duoc DB
                    console.log('Error:', error);
                    reject(error);
                    return;
                }
                for (i in result.rows) {
                    let record = result.rows[i];
                    if (temp[record.code_type] == undefined) {
                        temp[record.code_type] = {}
                    }
                    if (temp[record.code_type][record.value_code] == undefined) {
                        temp[record.code_type][record.value_code] = record;
                    }
                    
                }

                for (i in result.rows) {
                    const record = result.rows[i];
                    if (record.code_type == 'PROVINCE') {
                        if(!province.hasOwnProperty('SAPO')) {
                            province['SAPO'] = {}
                        }
                        if(!province.hasOwnProperty('OTHER')) {
                            province['OTHER'] = {}
                        }
                        if(record.val_code1 == 'SAPO') {
                            province['SAPO'][record.value_code] = record
                        }
                        else {
                            province['OTHER'][record.value_code] = record
                        }
                    }

                    if (record.code_type == 'DISTRICT') {
                        if(!district.hasOwnProperty('SAPO')) {
                            district['SAPO'] = {}
                        }
                        if(!district.hasOwnProperty('OTHER')) {
                            district['OTHER'] = {}
                        }
                        if(record.val_code1 == 'SAPO') {
                            district['SAPO'][record.value_code] = record
                        }
                        else {
                            district['OTHER'][record.value_code] = record
                        }
                    }

                    if (record.code_type == 'WARD') {
                        if(!ward.hasOwnProperty('SAPO')) {
                            ward['SAPO'] = {}
                        }
                        if(!ward.hasOwnProperty('OTHER')) {
                            ward['OTHER'] = {}
                        }
                        if(record.val_code1 == 'SAPO') {
                            ward['SAPO'][record.value_code] = record
                        }
                        else {
                            ward['OTHER'][record.value_code] = record
                        }
                    }

                    if (record.code_type == 'EMPLOYMENT_TYPE') {
                        employmentType[record.value_code] = record.value_name_en;
                        employmentTypeFull[record.value_code] = record;
                    }
                    if (record.code_type == 'TYPTEL') {
                        refType[record.value_code] = record.value_name_en;
                    }
                    if (record.code_type == 'BUNDLE') {
                        bundle[record.value_code] = record;
                    }
                    if (record.code_type == 'DOCUMENT') {
                        document[record.value_code] = record;
                    }
                    if (record.code_type == 'MISTAKE') {
                        mistake[record.value_code] = record;
                    }
                    if (record.code_type == 'PROFESSION') {
                        profession[record.value_code] = record
                    }
                    if (record.code_type == 'JOB_TYPE') {
                        jobType[record.value_code] = record
                    }
                    if (record.code_type == 'BUSINESS_TYPE') {
                        businessType[record.value_code] = record
                    }
                    if (record.code_type == 'BUSINESS_FORM') {
                        businessForm[record.value_code] = record
                    }
                    if (record.code_type == 'PAYMENT_METHOD_SMCA') {
                        paymentMethod[record.value_code] = record
                    }
                    if (record.code_type == 'PERIOD') {
                        period[record.value_code] = record
                    }
                    if (record.code_type == 'RESIDENCE_TYPE') {
                        residence_type[record.value_code] = record
                    }
                    if (record.code_type == 'CERTIFICATE_TYPE') {
                        certificate_type[record.value_code] = record
                    }
                    if (record.code_type == 'ASSETS_TYPE') {
                        assets_type[record.value_code] = record
                    }
                    if (record.code_type == 'PROPERTY_TYPE') {
                        property_type[record.value_code] = record
                    }
                }
                for (i in result.rows) {
                    let record = result.rows[i];
                    if (record.code_type == 'DISTRICT') {
                        if (area[record.value_code_parent] != undefined) {
                            if (area[record.value_code_parent].districts == undefined) {
                                area[record.value_code_parent].districts = {}
                            }
                            area[record.value_code_parent].districts[record.value_code] = record;
                        }

                    }
                }

                for (i in result.rows) {
                    let record = result.rows[i];
                    if (record.code_type == 'WARD') {
                        // Tim thang tinh
                        let matinh
                        if (temp['DISTRICT'] != undefined && temp['DISTRICT'][record.value_code_parent]) {
                            matinh = temp['DISTRICT'][record.value_code_parent]['value_code_parent'];
                        }
                        if (area[matinh] != undefined && area[matinh].districts[record.value_code_parent] != undefined) {

                            if (area[matinh].districts[record.value_code_parent].wards == undefined) {
                                area[matinh].districts[record.value_code_parent].wards = {}
                            }



                            area[matinh].districts[record.value_code_parent].wards[record.value_code] = record;
                        }

                    }
                }
                status = 1;
                
                resolve({
                    cache: {
                        'full': temp,
                        'area': area,
                        'employmentType': employmentType,
                        'refType': refType,
                        'bundle': bundle,
                        'document': document,
                        'mistake': mistake,
                        "province": province,
                        "district" : district,
                        "ward" : ward,
                        employmentTypeFull,
                        jobType,
                        profession,
                        businessType,
                        businessForm,
                        paymentMethod,
                        period,
                        residence_type,
                        certificate_type,
                        assets_type,
                        property_type
                    }, status: status
                });

            })
        } catch (e) {
            reject(e);
        }

    });
}

async function loadCacheV2(poolRead) {
    try {
        const sql = "select * from lov_allcode la where is_delt = 0"
        const queryResult = await poolRead.query(sql)
        const cache = {}
        for(idx in queryResult.rows) {
            var row = queryResult.rows[idx]
            if(!cache.hasOwnProperty(row.code_type)) {
                cache[row.code_type] = {}
            }
            cache[row.code_type][row.value_code] = {
                code : row.value_code,
                nameVn : row.value_name_vn,
                nameEn : row.value_name_en
            }
        }
        console.log("LOAD cache V2 successfully.")
        return cache
    }
    catch(err) {
        console.log(`init cache error : ${err.message}`)
        return false
    }
}

module.exports = {
    loadCache,
    loadCacheV2
}