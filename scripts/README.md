# Database Scripts

This directory contains scripts for database operations.

## Insert Document Data Script

### Overview
Script to insert document data into the `lov_allcode` table in PostgreSQL.

### Files
- `insert_document_data.js` - Node.js script for inserting document data
- `../data/insert_document_vldkunn.sql` - SQL script for direct database execution

### Usage

#### Node.js Script
```bash
# Run with default environment (dev)
node scripts/insert_document_data.js

# Run with specific environment
node scripts/insert_document_data.js dev
node scripts/insert_document_data.js uat
node scripts/insert_document_data.js prod
```

#### SQL Script
```bash
# Using psql command line
psql -h <host> -p <port> -U <username> -d <database> -f data/insert_document_vldkunn.sql

# Example for dev environment
psql -h *********** -p 5432 -U masterdata -d masterdata -f data/insert_document_vldkunn.sql
```

### Data Being Inserted
The script inserts the following record into `public.lov_allcode`:

```sql
INSERT INTO public.lov_allcode
(code_type, value_code, value_name_vn, value_name_en)
VALUES('DOCUMENT', 'VLDKUNN', 'Giấy đề nghị giải ngân kiêm KUNN', 'Giấy đề nghị giải ngân kiêm KUNN')
```

### Features
- **Duplicate Check**: Both scripts check if the record already exists before inserting
- **Environment Support**: Node.js script supports different environments (dev/uat/prod)
- **Error Handling**: Comprehensive error handling and logging
- **Transaction Safety**: Uses proper PostgreSQL transactions

### Prerequisites
- Node.js and npm installed
- PostgreSQL client (for SQL script)
- Environment variables configured in `.env.{environment}` files
- Database connection permissions

### Environment Variables Required
- `DB_HOST_WRITE` - Database host for write operations
- `DB_PORT` - Database port (default: 5432)
- `DB_DATABASE` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password

### Output
The Node.js script provides detailed console output including:
- Connection status
- Duplicate check results
- Insert operation results
- Error messages (if any)

### Error Handling
- Checks for existing records to prevent duplicates
- Validates database connection
- Provides detailed error messages
- Graceful cleanup of database connections
