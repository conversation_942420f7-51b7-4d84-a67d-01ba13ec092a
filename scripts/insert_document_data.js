#!/usr/bin/env node

/**
 * Script to insert document data into lov_allcode table
 * Usage: node scripts/insert_document_data.js [environment]
 * Environment: dev, uat, prod (default: dev)
 */

const { Pool } = require('pg');
require('dotenv').config({ path: `./.env.${process.env.NODE_ENV || 'dev'}` });

// Database configuration
const getDbConfig = () => {
  return {
    host: process.env.DB_HOST_WRITE,
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_DATABASE,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
  };
};

// Data to insert
const documentData = {
  code_type: 'DOCUMENT',
  value_code: 'VLDKUNN',
  value_name_vn: 'Giấy đề nghị giải ngân kiêm KUNN',
  value_name_en: '<PERSON><PERSON><PERSON><PERSON> đề nghị giải ngân kiêm KUNN'
};

async function insertDocumentData() {
  const pool = new Pool(getDbConfig());
  
  try {
    console.log('Connecting to database...');
    
    // Check if record already exists
    const checkSql = `
      SELECT COUNT(*) as count 
      FROM public.lov_allcode 
      WHERE code_type = $1 AND value_code = $2
    `;
    
    const checkResult = await pool.query(checkSql, [documentData.code_type, documentData.value_code]);
    const recordExists = parseInt(checkResult.rows[0].count) > 0;
    
    if (recordExists) {
      console.log(`Record with code_type='${documentData.code_type}' and value_code='${documentData.value_code}' already exists.`);
      console.log('Skipping insert operation.');
      return;
    }
    
    // Insert new record
    const insertSql = `
      INSERT INTO public.lov_allcode
      (code_type, value_code, value_name_vn, value_name_en, value_desc, is_delt, created_date, created_user, owner_id)
      VALUES($1, $2, $3, $4, $5, $6, NOW(), $7, $8)
      RETURNING id, code_type, value_code, value_name_vn, value_name_en
    `;
    
    const insertParams = [
      documentData.code_type,
      documentData.value_code,
      documentData.value_name_vn,
      documentData.value_name_en,
      documentData.value_name_vn, // Using Vietnamese name as description
      0, // is_delt = 0 (not deleted)
      'system', // created_user
      1 // owner_id
    ];
    
    console.log('Inserting document data...');
    const insertResult = await pool.query(insertSql, insertParams);
    
    if (insertResult.rows.length > 0) {
      console.log('✅ Successfully inserted document data:');
      console.log(JSON.stringify(insertResult.rows[0], null, 2));
    } else {
      console.log('❌ Insert operation completed but no data returned.');
    }
    
  } catch (error) {
    console.error('❌ Error inserting document data:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await pool.end();
    console.log('Database connection closed.');
  }
}

// Main execution
if (require.main === module) {
  const environment = process.argv[2] || 'dev';
  process.env.NODE_ENV = environment;
  
  console.log(`🚀 Starting document data insertion script for environment: ${environment}`);
  console.log(`Database: ${process.env.DB_DATABASE}@${process.env.DB_HOST_WRITE}:${process.env.DB_PORT}`);
  
  insertDocumentData()
    .then(() => {
      console.log('✅ Script completed successfully.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { insertDocumentData };
