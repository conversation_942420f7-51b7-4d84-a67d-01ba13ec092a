
const getSmeMappingCode = async ({key, partnerCode, valueCode}) => {
	// const sql = `select sm.ref_type, sm.ref_code, la.code_type, la.value_code,
    // la.value_name_en, la.value_name_en,la.value_desc, la.value_code_parent
    // FROM sme_mapping sm
    // JOIN lov_allcode la on sm.ref_type = la.code_type and sm.ref_code = la.value_code
    // where sm.ref_type = $1 and sm.partner_code = $2 and sm.is_delt = $3 and la.is_delt =$3`;
    const sql = `select * from sme_mapping where is_delt =$1 and "key" = $2 and partner_code = $3 and value_code = $4`;
    const poolRead = global.poolRead
	const data = await poolRead.query(sql,[0, key, partnerCode, valueCode])
	.then(result => {
		return result;
	})
    return data?.rows?.[0];
}

const getSmeConfigKey = async()=>{
    const sql = `select * from sme_config_key where is_delt =$1`;
    const poolRead = global.poolRead
	const data = await poolRead.query(sql,[0])
	.then(result => {
		return result;
	})
    return data?.rows ?? [];
}

module.exports = {
	getSmeMappingCode,
    getSmeConfigKey
}