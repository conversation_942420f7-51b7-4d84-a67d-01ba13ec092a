{"name": "masterdata", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "node main.js", "debug": "NODE_ENV=uat nodemon main.js", "prod": "node main.js"}, "repository": {"type": "git", "url": "git+https://github.com/EVNFC/masterdata.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/EVNFC/masterdata/issues"}, "homepage": "https://github.com/EVNFC/masterdata#readme", "dependencies": {"axios": "^1.5.1", "camelcase-keys": "^6.2.2", "dateformat": "^4.5.1", "dotenv": "^17.2.0", "express": "^4.17.1", "http": "0.0.1-security", "pg": "^8.5.1"}, "devDependencies": {"nodemon": "^3.1.10"}}