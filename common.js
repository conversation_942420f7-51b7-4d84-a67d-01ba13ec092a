/**
 * Thu vien su dung cac ham de goi API, lay configuration, ghi log, push notification
 */
const dateFormat = require('dateformat');
// const request = require('request');
const axios = require('axios');
let moduleName;
/**
 * Ham set ten service cho common.
 * @param {*} name 
 */
const setServiceName = function (name) {
    moduleName = name;
}
/**
 * Ham ghi log
 * @param {*} event 
 * @param {*} l 
 */
const log = function (event, l) {
    if (l == undefined) {
        level = 'info'
    }
    let day = dateFormat(new Date(), "yyyymmdd HH:MM:ss");
    let str = day + '|' + moduleName + '|' + level + '|' + event;
    console.log(str);
}
const getAPI = function (link, header) {
    return new Promise(function (resolve, reject) {
        try {
            let reqObj = {
                url: link,
                method: 'GET'
            };

            if (header != undefined) {
                reqObj.headers = header;
            }
            axios(reqObj)
                .then(function (response) {
                resolve(response.data)
                })
                .catch(function (error) {
                console.log('Error getAPI:', link)
                // console.log(error.message);
                    if (error.response) {
                        resolve(error.response.data)
                    } else {
                        reject(error)
                    }
            })
            // request(reqObj, (error, response1, body) => {
            //     if (error) {
            //         reject(error);
            //     } else {
            //         resolve(JSON.parse(body));
            //     }
            // })
        } catch (e) {
            reject(e);
        }
    }
    )
}
// const postAPI = function (link, json, header) {
//     return new Promise(function (resolve, reject) {
//         try {
//             let reqObj = {
//                 url: link,
//                 method: 'POST'
//             };

//             if (json != undefined) {
//                 reqObj.json = json;
//             }
//             if (header != undefined) {
//                 reqObj.headers = header;
//             }
//             request(reqObj, (error, response1, body) => {
//                 if (error) {
//                     reject(error);
//                 } else {
//                     resolve(body);
//                 }
//             })
//         } catch (e) {

//             reject(e);
//         }
//     }
//     )
// }

// const updateAPI = function (link, body, header) {
//     return new Promise(function (resolve, reject) {
//         try {
//             try {
//                 let reqObj = {
//                     url: link,
//                     method: 'UPDATE'
//                 };

//                 if (json != undefined) {
//                     reqObj.json = json;
//                 }
//                 if (header != undefined) {
//                     reqObj.headers = header;
//                 }
//                 request(reqObj, (error, response1, body) => {
//                     if (error) {
//                         reject(error);
//                     } else {
//                         resolve(body);
//                     }
//                 })
//             } catch (e) {

//                 reject(e);
//             }


//         } catch (e) {

//             reject(e);
//         }
//     }
//     )
// }
// const deleteAPI = function (link, body, header) {
//     return new Promise(function (resolve, reject) {
//         try {

//             let reqObj = {
//                 url: link,
//                 method: 'DELETE'
//             };

//             if (json != undefined) {
//                 reqObj.json = json;
//             }
//             if (header != undefined) {
//                 reqObj.headers = header;
//             }
//             request(reqObj, (error, response1, body) => {
//                 if (error) {
//                     reject(error);
//                 } else {
//                     resolve(body);
//                 }
//             })
//         } catch (e) {
//             reject(e);
//         }
//     }
//     )
// }
const getConfiguration = function (link) {
    return new Promise(function (resolve, reject) {
        try {



        } catch (e) {

            reject(e);
        }


    });
}

const levenshteinDistance = (s, t) => {
    if (!s.length) return t.length;
    if (!t.length) return s.length;
    const arr = [];
    for (let i = 0; i <= t.length; i++) {
      arr[i] = [i];
      for (let j = 1; j <= s.length; j++) {
        arr[i][j] =
          i === 0
            ? j
            : Math.min(
                arr[i - 1][j] + 1,
                arr[i][j - 1] + 1,
                arr[i - 1][j - 1] + (s[j - 1] === t[i - 1] ? 0 : 1)
              );
      }
    }
    return arr[t.length][s.length];
  };
module.exports = {
    setServiceName,
    log,
    getConfiguration,
    // deleteAPI,
    // updateAPI,
    // postAPI,
    getAPI,
    levenshteinDistance
}

