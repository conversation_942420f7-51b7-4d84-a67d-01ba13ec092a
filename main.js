/**
 * Service insert log.
 */
var express = require('express');
var app = express();
var bodyParser = require('body-parser');
app.use(bodyParser());
var httpServer = require('http').Server(app);
const businessLogic = require('./businessLogic.js');
const initBlock = require('./initBlock.js');
const common = require('./common.js');
const Pool = require('pg').Pool;
const serviceName = 'masterData';
common.setServiceName(serviceName);
require('dotenv').config({ path: `./.env.${process.env.NODE_ENV}` });
 // Thiet lap ten module
// Cau hinh tro toi Configuration service;
const configurationServiceLink = `https://dev-els-nlb.easycredit.vn:1025/services?service=` + serviceName;

// var
//init block

var poolWrite;
var poolRead;
var cache;
var config;
var status = 0;
common.getAPI(configurationServiceLink, { 'Content-Type': 'application/json' }).then(async data => {
    if (data == undefined) {
        status = -1;// Khong lay duoc cau hinh
        common.log('Khong lay duoc cau hinh:');
    } else {
        config = data;
        poolWrite = new Pool(config.data.databaseWriteOnly);
        poolRead = new Pool(config.data.databaseReadOnly);
        const cacheV2 = await initBlock.loadCacheV2(poolRead)
        global.cacheV2 = cacheV2;
        global.poolRead = poolRead;
        global.poolWrite = poolWrite;
        initBlock.loadCache(poolRead).then(data => {
            if (data.status == 1) {
                cache = data.cache;

                status = 1; // Thiet lap status de dich vu san sang.
                httpServer.listen(config.data.http.port); // Load xong cache ... thi bat dich vu.
                //httpServer.listen(1000)
                common.log('Service phuc vu o port:' + config.data.http.port);
            } else {
                common.log("He thong khoi dong khong thanh cong")
            }
        }).catch(error => {
            common.log("He thong khoi dong khong thanh cong:" + error)
        });
    }
}).catch(error => {
    console.log(error)
    common.log('Khong lay duoc cau hinh:' + error);
    status = -1;

})

app.post('/masterdata/v1/reloadCache',async (req,res) => {
    try {
        const data = await initBlock.loadCache(poolRead)
        if(data.status == 1) {
            cache = data.cache
        }
        return res.status(200).json({
            code : 1,
            msg : "reload cache successfully."
        })
    }
    catch (error) {
        return res.status(500).json({
            code : 0,
            msg : "reload cache error"
        })
    }
})

app.use(function (req, res, next) {
    req.poolWrite = poolWrite;
    req.poolRead = poolRead;
    req.config = config;
    req.cache = cache
    next();
})


app.get('/masterdata/v1/businessunits', (req, res) => {
    try {
        businessLogic.getBusinessUnit(req, res, cache, common);
    } catch (e) {
        common.log(e);
        res.status(502).json({
            error: 1,
            msg: 'Service Error'
        })
    }
})

app.get('/masterdata/v1/provinces', (req, res) => {
    try {
        businessLogic.getProvince(req, res, cache, common);
    } catch (e) {
        common.log(e);
        res.status(502).json({
            error: 1,
            msg: 'Service Error'
        })
    }
})

app.get('/masterdata/v1/districts', (req, res) => {
    try {
        businessLogic.getDistrict(req, res, cache, common);
    } catch (e) {
        common.log(e);
        res.status(502).json({
            error: 1,
            msg: 'Service Error'
        })
    }
})

app.get('/masterdata/v1/wards', (req, res) => {
    try {
        businessLogic.getWard(req, res, cache, common);
    } catch (e) {
        common.log(e);
        res.status(502).json({
            error: 1,
            msg: 'Service Error'
        })
    }
})

app.get("/masterdata/v1/employmentType", (req,res) => {
    businessLogic.getEmploymentType(req,res,cache,common)
})

app.get("/masterdata/v1/refType", (req,res) => {
    businessLogic.getRefType(req,res,cache,common)
})
app.get("/masterdata/v1/bundle", (req,res) => {
    businessLogic.getBundle(req,res,cache,common)
})
app.get("/masterdata/v1/document", (req,res) => {
    businessLogic.getDocument(req,res,cache,common)
})
app.get("/masterdata/v1/mistake", (req,res) => {
    businessLogic.getMistake(req,res,cache,common)
})
app.get("/masterdata/v1/bundle/detail", (req,res) => {
    businessLogic.getBundleDetail(req,res,cache,common,poolRead)
})

app.get("/masterdata/v1/bank", (req,res) => {
    businessLogic.getBankInfo(req,res,cache,common,poolWrite)
})

app.get("/masterdata/v1/mistakeList", (req,res) => {
    businessLogic.getMistakeList(req,res,cache,common,poolWrite)
})


app.get("/masterdata/v1/getPlace", (req,res) => {
    businessLogic.getPlace(req,res,cache,common,poolWrite)
})

app.get("/masterdata/v1/getMarriedStatus", (req,res) => {
    businessLogic.getMarriedStatus(req,res,cache,common,poolWrite)
})

app.get("/masterdata/v1/getValueCode", (req,res) => {
    businessLogic.getValueCode(req,res,cache,common,poolWrite)
})

app.get("/masterdata/v2/getValueCode", (req,res) => {
    businessLogic.getValueCodeVer2(req, res, poolWrite)
})

app.get("/masterdata/v1/get-value-code-v2", businessLogic.getValueCodeV2)

app.get("/masterdata/v1/getPlaceInfo", (req,res) => {
    businessLogic.getPlaceInfo(req,res,cache,common,poolWrite)
})

app.get("/masterdata/v1/importData", (req,res) => {
    businessLogic.importData(req,res,cache,common,poolWrite)
})

app.post("/masterdata/v1/getValueCodeByValueName", (req,res) => {
    businessLogic.searchValueCodeByValueName(req, res)
})

app.post("/masterdata/v1/getAddressCodeByAddress", (req,res) => {
    businessLogic.getAddressCodeByAddress(req, res)
})

app.post("/masterdata/v1/getAddressCodeByAddresses", (req,res) => {
    businessLogic.getAddressCodeByAddresses(req, res)
})


app.get('/masterdata/v1/cache', (req, res) => {
    try {
        res.status(200).json({
            error: 0,
            msg: 'cache',
            cache: cache.area
        })
    } catch (e) {
        common.log(e);
        res.status(502).json({
            error: 1,
            msg: 'Service Error'
        })
    }
})

app.get('/masterdata/v1/healthcheck', (req, res) => {
    res.status(200).json({
        error: 0,
        msg: 'Master data service is alive'
    })
});

const getDocleService = require("./bundle/getDocument")
const getBundleService = require("./bundle/getBundle")

app.get('/masterdata/v1/getDocumentByBundle', (req,res) => {
    getDocleService.getDocument(req,res,poolRead)
})

app.get('/masterdata/v1/bundleByDocument', (req,res) => {
    getBundleService.getBundleByDocument(req,res,poolRead)
})

const getPlaceService = require("./area/getPlace")
app.get("/masterdata/v1/province/provinceList",getPlaceService.getProvince)
app.get("/masterdata/v1/districts/districtList",getPlaceService.getDistrict)
app.get("/masterdata/v1/wards/wardList",getPlaceService.getWard)
app.post("/masterdata/v1/place/address",getPlaceService.getAddress)

app.get("/masterdata/v2/address/provinces",getPlaceService.getNewProvinces)
app.get("/masterdata/v2/address/communes",getPlaceService.getCommunes)
app.get("/masterdata/v2/address/wards",getPlaceService.getNewWards)
app.post("/masterdata/v2/place/address",getPlaceService.getAddressV2)

const jobService = require("./job/jobInfo")
app.get("/masterdata/v1/job/employmentType",jobService.getEmploymentType)
app.get("/masterdata/v1/job/jobType",jobService.getJobtype)
app.get("/masterdata/v1/job/profession",jobService.getProfession)

const mistakeService = require("./mistake/mistake")
app.get("/masterdata/v1/mistake/mistakeList",mistakeService.getMistakeList)
app.get("/masterdata/v1/lov",businessLogic.getLov)
app.get("/masterdata/v2/lov",businessLogic.getLovVer2)
app.get("/masterdata/v3/lov",businessLogic.getLovVerFullInfo)


// lay status_case
const statusCaseService = require("./status-case/statusCase")
app.get('/masterdata/v1/case-status/stepName', statusCaseService.getListStepName)
app.get('/masterdata/v1/case-status/action', statusCaseService.getListAction)
app.get('/masterdata/v1/case-status/detail', statusCaseService.getCaseStatusValue)
app.get('/masterdata/v1/case-status', statusCaseService.getFullCaseStatus)

app.get("/masterdata/v1/bank/bankbranch",businessLogic.getBankBranchLov)
app.get("/masterdata/v1/find-all",businessLogic.getListByCodeType)

app.get("/masterdata/v1/sme-employment-type/type4",businessLogic.getSmeEmploymentType4Lov)

// get business form
app.get("/masterdata/v1/get-business-form", businessLogic.getBusinessForm)
app.get("/masterdata/v1/get-business-type", businessLogic.getBusinessType)
app.get("/masterdata/v1/get-period", businessLogic.getPeriod)
app.get("/masterdata/v1/get-payment-method", businessLogic.getPaymentMethod)
app.get("/masterdata/v1/get-residence-type", businessLogic.getResidenceType)
app.get("/masterdata/v1/get-documents", businessLogic.getDocuments)
app.get("/masterdata/v1/get-certificate-type", businessLogic.getCertificateType)
app.get("/masterdata/v1/get-assets-type", businessLogic.getAssetsType)
app.get("/masterdata/v1/get-property-type", businessLogic.getPropertyType)
app.post("/masterdata/v1/sme/mapping-code", (req,res) => {
    businessLogic.mappingCodeSme(req,res)
})